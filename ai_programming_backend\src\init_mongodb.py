import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from pymongo import MongoClient
from src.models.mongodb_models import mongodb

# Define the MongoDB connection string
MONGODB_CONNECTION_STRING = "mongodb://localhost:27017/"

def init_mongodb():
    """Initialize MongoDB data."""
    try:
        # Establish a connection to MongoDB
        client = MongoClient(MONGODB_CONNECTION_STRING)
        # Access the database (you can change 'your_database_name' to your actual database name)
        db = client['your_database_name']
        
        # Pass the database instance to the init_data method if needed
        mongodb.init_data(db)
        print("MongoDB initialization completed!")
    except Exception as e:
        print(f"Error initializing MongoDB: {e}")
    finally:
        # Close the MongoDB connection
        client.close()

if __name__ == '__main__':
    init_mongodb()

