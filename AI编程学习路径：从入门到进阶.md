# AI编程学习路径：从入门到进阶

## Part1：基础知识
*   **01 Windsurf做的产品**: 介绍Windsurf相关产品。
*   **02 AI编程构成了双杠杆的重要一环**: 强调AI编程在产品开发中的重要性。
*   **03 不管黑猫白猫，做出产品就是好猫**: 强调以结果为导向，做出实际产品的重要性。
*   **04 Windsurf，不是Cursor？**: 区分Windsurf和Cursor。
*   **05 Trae可免费使用Claude 3.7**: 介绍免费使用Claude 3.7的途径。
*   **06 必备的Windsurf技巧**: 学习Windsurf的基本操作和技巧。
    *   第一，开始的开始，是先下载软件
    *   第二，在你的电脑上，新建个文件夹...
    *   第三，让AI更听话：Set Global AI Rules
    *   第四，安装中文插件
*   **07 一些AI编程的心得**: 分享AI编程的经验和心得。
    *   你写的越慢，你写的越快

## Part2：使用AI IDE作为你的个人工作台
*   **01 使用AI IDE进行创作**: 学习如何使用AI IDE进行创作。
    *   改写提示词
*   **02 使用提示词生成精美图片**: 学习如何使用提示词生成图片。
    *   最终版本的提示词
*   **03 10分钟搞定高德地图MCP！我用AI解决...**: 实际案例，解决高德地图MCP问题。
*   **免费的社群**: 加入免费社群获取更多资源。

## Part3：网页开发
*   **01 图片字幕生成器**: 使用多模态复制产品。
*   **02 注册硅基流动，赠4000万token**: 注册并获取token。
*   **03 帮小红书的老外起中文名**: 使用WorkSpace AI Rules接入大模型API。
*   **04 做一个专属的好文推荐网站 (DeepSeek R1 + 飞书多维表格)**: 创建带AI能力的飞书多维表格，用网页呈现多维表格里的内容。
*   **05 做一档你自己的AI播客**: 语音复刻。

## Part4：浏览器插件
*   **01 DeepSeek驱动的网页金句卡片生成**: 先Chat，再开发，插件安装指南，Chrome插件logo生成。
*   **02 复刻善思flomo**: 接入flomo API，更新readme，使用Git存档。

## Part5：微信小程序
*   **01 做一个简单的微信小程序**: 开发小程序准备工作，小程序的认证和备案。
*   **02 书单星球 (微信小程序)**: 微信小程序开发路径。

## Part6：基于扣子搭建产品
*   **01 扣子Coze产品介绍**: 介绍Coze产品。
*   **02 用DeepSeek R1，抖音视频文案提取+改写**: 理解Function Call，简单智能体搭建。
*   **03 柴犬表情包生成器实战**: Coze的协同机制，完整智能流搭建流程，前端调通Coze API。

## Part7：部署
*   **01 将网页产品部署到Cloudflare!**: 部署Cloudflare原理，完整部署流程。
*   **02 使用Github+Vercel免费部署**: 开始推荐Github+Vercel，简单配置部署为环境变量。

